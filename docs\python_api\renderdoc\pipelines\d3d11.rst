API Reference: D3D11 Pipeline State
====================================

This is the API reference for the functions, classes, and enums in the ``renderdoc`` module which represents the underlying interface that the UI is built on top of. For more high-level information and instructions on using the python API, see :doc:`../../index`.

.. contents:: Sections
   :local:

.. currentmodule:: renderdoc

.. autoclass:: D3D11State
  :members:

Vertex Input
------------

.. autoclass:: D3D11InputAssembly
  :members:

.. autoclass:: D3D11IndexBuffer
  :members:

.. autoclass:: D3D11VertexBuffer
  :members:

.. autoclass:: D3D11Layout
  :members:

Shaders and Bindings
--------------------

.. autoclass:: D3D11Shader
  :members:

Stream-out
----------

.. autoclass:: D3D11StreamOut
  :members:

.. autoclass:: D3D11StreamOutBind
  :members:

Rasterizer
----------

.. autoclass:: D3D11Rasterizer
  :members:

.. autoclass:: D3D11RasterizerState
  :members:

Output Merger
-------------

.. autoclass:: D3D11OutputMerger
  :members:

.. autoclass:: D3D11DepthStencilState
  :members:

.. autoclass:: D3D11BlendState
  :members:

Predication
-----------

.. autoclass:: D3D11Predication
  :members:
