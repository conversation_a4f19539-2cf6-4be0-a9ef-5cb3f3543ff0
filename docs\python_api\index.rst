Python API
==========

RenderDoc exposes APIs to python at two different levels:

1. The :doc:`base replay API <renderdoc/index>`, the ``renderdoc`` module, which provides low level access to handling capture files, replaying frames and obtaining analysis information. The UI is built entirely on top of this API, so it provides the full power of RenderDoc, however is does not have many convenience abstractions.
2. The :doc:`RenderDoc UI API <qrenderdoc/index>`, the ``qrenderdoc`` module, which exposes the abstractions and panels within the UI tool.

Within RenderDoc - when either running scripts on the command line, or via the :doc:`../window/python_shell` - both modules are pre-imported and available automatically.

It is also possible to build the ``renderdoc`` module standalone which can be loaded into python and used for scripting directly without the UI program. Due to the inherent difficulty of distributing C python modules this isn't included by default in distributed builds at the time of writing, but is generated by default in source builds - ``renderdoc.pyd`` on windows or ``renderdoc.so`` elsewhere. Use of this module is strictly a convenience and is not supported.

.. note::

    Due to Android being inherently an unstable and unreliable platform, using the python scripting on Android devices is not recommended or supported. It may work, but you'll be on your own with any problems encountered as they are too likely to be caused by problems on Android.

You must use exactly the same version of python to load the module as was used to build it.

On windows by default RenderDoc builds against python 3.6 which is what it's distributed with.

This can be overridden by setting an overridden path under the ``Python Configuration`` section in the properties of the ``qrenderdoc`` project and ``pyrenderdoc_module`` project. It must point to a python installation.

RenderDoc requires pythonXY.lib, include files such as include/Python.h, as well as a .zip of the standard library. If you installed python with an installer you have the first two, and can generate the standard library zip by zipping the contents of the Lib folder. If you downloaded the embeddable zip distribution you will only have the standard library zip, you need to obtain the include files and ``.lib`` file separately.

.. note::

    RenderDoc only supports Python 3.4+, Python 2 is not supported.

This documentation contains information on getting started with the scripting, as well as tutorials and examples outline how to perform simple tasks.

Each example has a simple motivating goal and shows how to achieve it using the interfaces provided. They will not show every possible use of the interfaces, but instead give a starting point to build on. Further information about exactly what functionality is available can be found in the API reference below as well as using the python built-in ``help()`` function.

.. toctree::
    examples/renderdoc_intro
    examples/qrenderdoc_intro
    dev_environment
    ui_extensions
    descriptors_bindings
    examples/basics
    examples/renderdoc/index
    examples/qrenderdoc/index
    renderdoc/index
    qrenderdoc/index
