# Filing feature requests

:warning: **PLEASE NOTE: YOU MUST USE THE PROVIDED ISSUE TEMPLATE!** :warning:

When filing a bug please click 'Get started' next to the 'Feature request' entry. Below is guidance for each section of the issue template.

# Description of Feature

When writing the description of a feature request, it's important to describe what your end goal is. If you describe only what you think the solution should be then that makes it harder to understand what you're really after. This is often known as [the XY problem](http://xyproblem.info/). You can of course describe the solution you have in mind as well, just be sure not to _only_ describe that solution.

Sometimes there is an improvement that may work for you but may solve other problems as well. Or perhaps the solution you have in mind might have unintended side-effects. If you describe what your workflow is and what you're trying to do then it's much easier to understand where you're coming from.

This can also be a reason why feature requests are turned down. If the problem you are running into is particularly niche, or you want behaviour one way but it's mutually exclusive with behaviour a different way that is generally better for users as a whole, that may be reason to turn down the request. Features don't come for free and even adding alternate options still leads to maintenance and UX burdens.

If this happens please be aware that it's not a criticism of your use case, and if your change is small or you feel adventurous you could change RenderDoc's source and recompile it to behave as you'd like.

If you can upload a capture that exhibits exactly what problem you're trying to solve that would be very welcome :smile:.

# Environment

Please update the environment section to at least list the main three items - the RenderDoc version you are using, your OS, and the graphics API(s) you are interested in.

This gives context to what you're asking for, even in cases where the feature may apply to all APIs or OSs. The RenderDoc version is also relevant in case you haven't tried the latest version where something may have changed, or if there has been work in this area since the last version was released.
