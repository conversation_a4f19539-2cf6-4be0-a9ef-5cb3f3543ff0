{#
    basic/search.html
    ~~~~~~~~~~~~~~~~~

    Template for the search page.

    :copyright: Copyright 2007-2013 by the Sphinx team, see AUTHORS.
    :license: BSD, see https://github.com/sphinx-doc/sphinx/blob/master/LICENSE for details.
#}
{%- extends "layout.html" %}
{% set title = _('Search') %}
{% set display_vcs_links = False %}
{%- block scripts %}
    {{ super() }}
    <script type="text/javascript" src="{{ pathto('_static/searchtools.js', 1) }}"></script>
    <script type="text/javascript" src="{{ pathto('_static/language_data.js', 1) }}"></script>
{%- endblock %}
{% block footer %}
  <script type="text/javascript">
    jQuery(function() { Search.loadIndex("{{ pathto('searchindex.js', 1) }}"); });
  </script>
  {# this is used when loading the search index using $.ajax fails,
     such as on Chrome for documents on localhost #}
  <script type="text/javascript" id="searchindexloader"></script>
  {{ super() }}
{% endblock %}
{% block body %}
  <noscript>
  <div id="fallback" class="admonition warning">
    <p class="last">
      {% trans trimmed %}Please activate JavaScript to enable the search
      functionality.{% endtrans %}
    </p>
  </div>
  </noscript>

  {% if search_performed %}
    {# Translators: Search is a noun, not a verb #}
    <h2>{{ _('Search Results') }}</h2>
    {% if not search_results %}
      <p>{{ _('Your search did not match any documents. Please make sure that all words are spelled correctly and that you\'ve selected enough categories.') }}</p>
    {% endif %}
  {% endif %}
  <div id="search-results">
  {% if search_results %}
    <ul>
    {% for href, caption, context in search_results %}
      <li>
        <a href="{{ pathto(item.href) }}">{{ caption }}</a>
        <p class="context">{{ context|e }}</p>
      </li>
    {% endfor %}
    </ul>
  {% endif %}
  </div>
{% endblock %}
