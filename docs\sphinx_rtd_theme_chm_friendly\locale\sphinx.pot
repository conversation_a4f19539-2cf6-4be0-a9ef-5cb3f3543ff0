# Translations template for sphinx_rtd_theme.
# Copyright (C) 2021 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2021.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 0.5.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2021-01-04 13:48-0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"

#: sphinx_rtd_theme/breadcrumbs.html:45 sphinx_rtd_theme/breadcrumbs.html:47
msgid "Edit on GitHub"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:52 sphinx_rtd_theme/breadcrumbs.html:54
msgid "Edit on Bitbucket"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:59 sphinx_rtd_theme/breadcrumbs.html:61
msgid "Edit on GitLab"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:64 sphinx_rtd_theme/breadcrumbs.html:66
msgid "View page source"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:76 sphinx_rtd_theme/footer.html:5
msgid "Next"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:79 sphinx_rtd_theme/footer.html:8
msgid "Previous"
msgstr ""

#: sphinx_rtd_theme/footer.html:20
#, python-format
msgid "&#169; <a href=\"%(path)s\">Copyright</a> %(copyright)s."
msgstr ""

#: sphinx_rtd_theme/footer.html:22
#, python-format
msgid "&#169; Copyright %(copyright)s."
msgstr ""

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:29
msgid "Build"
msgstr ""

#. the phrase "revision" comes from Git, referring to a commit
#: sphinx_rtd_theme/footer.html:35
msgid "Revision"
msgstr ""

#: sphinx_rtd_theme/footer.html:40
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr ""

#. the variable "sphinx_web" is a link to the Sphinx project documentation with
#. the text "Sphinx"
#: sphinx_rtd_theme/footer.html:52
#, python-format
msgid "Built with %(sphinx_web)s using a"
msgstr ""

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documenation
#: sphinx_rtd_theme/footer.html:54
msgid "theme"
msgstr ""

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:56
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr ""

#: sphinx_rtd_theme/layout.html:85
#, python-format
msgid "Search within %(docstitle)s"
msgstr ""

#: sphinx_rtd_theme/layout.html:93
msgid "About these documents"
msgstr ""

#: sphinx_rtd_theme/layout.html:96
msgid "Index"
msgstr ""

#: sphinx_rtd_theme/layout.html:99 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr ""

#: sphinx_rtd_theme/layout.html:102
msgid "Copyright"
msgstr ""

#: sphinx_rtd_theme/layout.html:134
msgid "Logo"
msgstr ""

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr ""

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr ""

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words "
"are spelled correctly and that you've selected enough categories."
msgstr ""

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr ""

#: sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr ""

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr ""

#. The phrase "Read the Docs" is not translated
#: sphinx_rtd_theme/versions.html:24
msgid "On Read the Docs"
msgstr ""

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr ""

#: sphinx_rtd_theme/versions.html:29
msgid "Builds"
msgstr ""

