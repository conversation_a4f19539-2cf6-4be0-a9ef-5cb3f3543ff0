API Reference: D3D12 Pipeline State
====================================

This is the API reference for the functions, classes, and enums in the ``renderdoc`` module which represents the underlying interface that the UI is built on top of. For more high-level information and instructions on using the python API, see :doc:`../../index`.

.. contents:: Sections
   :local:

.. currentmodule:: renderdoc

.. autoclass:: D3D12State
  :members:

Vertex Input
------------

.. autoclass:: D3D12InputAssembly
  :members:

.. autoclass:: D3D12Layout
  :members:

.. autoclass:: D3D12VertexBuffer
  :members:

.. autoclass:: D3D12IndexBuffer
  :members:

Shader
------

.. autoclass:: D3D12Shader
  :members:

Root Signature
--------------

.. autoclass:: D3D12RootSignature
  :members:

.. autoclass:: D3D12RootParam
  :members:

.. autoclass:: D3D12RootTableRange
  :members:

.. autoclass:: D3D12StaticSampler
  :members:

Stream-out
----------

.. autoclass:: D3D12StreamOut
  :members:

.. autoclass:: D3D12StreamOutBind
  :members:

Rasterizer
----------

.. autoclass:: D3D12Rasterizer
  :members:

.. autoclass:: D3D12RasterizerState
  :members:

Output Merger
-------------

.. autoclass:: D3D12OM
  :members:

.. autoclass:: D3D12DepthStencilState
  :members:

.. autoclass:: D3D12BlendState
  :members:

Resource States
---------------

.. autoclass:: D3D12ResourceData
  :members:

.. autoclass:: D3D12ResourceState
  :members:

