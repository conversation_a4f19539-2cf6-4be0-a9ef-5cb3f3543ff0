# English translations for sphinx_rtd_theme.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# <PERSON><PERSON>ic <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 0.4.3.dev0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2021-01-04 13:48-0800\n"
"PO-Revision-Date: 2019-07-16 21:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: French (https://www.transifex.com/readthedocs/teams/101354/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: sphinx_rtd_theme/breadcrumbs.html:45 sphinx_rtd_theme/breadcrumbs.html:47
msgid "Edit on GitHub"
msgstr "Éditer sur GitHub"

#: sphinx_rtd_theme/breadcrumbs.html:52 sphinx_rtd_theme/breadcrumbs.html:54
msgid "Edit on Bitbucket"
msgstr "Éditer sur Bitbucket"

#: sphinx_rtd_theme/breadcrumbs.html:59 sphinx_rtd_theme/breadcrumbs.html:61
msgid "Edit on GitLab"
msgstr "Éditer sur GitLab"

#: sphinx_rtd_theme/breadcrumbs.html:64 sphinx_rtd_theme/breadcrumbs.html:66
msgid "View page source"
msgstr "Afficher la source de la page"

#: sphinx_rtd_theme/breadcrumbs.html:76 sphinx_rtd_theme/footer.html:5
msgid "Next"
msgstr "Suivant"

#: sphinx_rtd_theme/breadcrumbs.html:79 sphinx_rtd_theme/footer.html:8
msgid "Previous"
msgstr "Précédent"

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:29
msgid "Build"
msgstr "Compilation"

#. the phrase "revision" comes from Git, referring to a commit
#: sphinx_rtd_theme/footer.html:35
msgid "Revision"
msgstr "Révision"

#: sphinx_rtd_theme/footer.html:40
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Dernière mise à jour le %(last_updated)s."

#. the variable "sphinx_web" is a link to the Sphinx project documentation
#. with
#. the text "Sphinx"
#: sphinx_rtd_theme/footer.html:52
#, python-format
msgid "Built with %(sphinx_web)s using a"
msgstr "Compilé avec %(sphinx_web)s en utilisant un"

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documenation
#: sphinx_rtd_theme/footer.html:54
msgid "theme"
msgstr "thème"

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:56
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr "fourni par %(readthedocs_web)s"

#: sphinx_rtd_theme/layout.html:85
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Rechercher dans %(docstitle)s"

#: sphinx_rtd_theme/layout.html:93
msgid "About these documents"
msgstr "À propos de cette documentation"

#: sphinx_rtd_theme/layout.html:96
msgid "Index"
msgstr "Index"

#: sphinx_rtd_theme/layout.html:99 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr "Rechercher"

#: sphinx_rtd_theme/layout.html:102
msgid "Copyright"
msgstr "Droits d'auteur"

#: sphinx_rtd_theme/layout.html:134
msgid "Logo"
msgstr "Logo"

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr "Activez JavaScript pour accéder à la fonction de recherche."

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr "Résultats de la recherche"

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""
"Votre recherche ne correspond à aucun document. Assurez-vous que tous les "
"mots sont correctement orthographiés et que vous avez sélectionné "
"suffisamment de catégories."

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr "Rechercher docs"

#: sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr "Versions"

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr "Téléchargements"

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr "Accueil du projet"

#: sphinx_rtd_theme/versions.html:29
msgid "Builds"
msgstr "Compilations"

#~ msgid "Docs"
#~ msgstr "Docs"

#~ msgid "Free document hosting provided by"
#~ msgstr "Hébergement gratuit de documents fourni par"
