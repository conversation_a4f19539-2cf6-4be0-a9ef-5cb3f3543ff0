/* ------------------------------------------------------------
 * python.swg
 *
 * Python configuration module.
 * ------------------------------------------------------------ */

/* ------------------------------------------------------------
 *  Inner macros
 * ------------------------------------------------------------ */
%include <pymacros.swg>


/* ------------------------------------------------------------
 *  The runtime part
 * ------------------------------------------------------------ */
%include <pyruntime.swg>

/* ------------------------------------------------------------
 *  Special user directives
 * ------------------------------------------------------------ */
%include <pyuserdir.swg>

/* ------------------------------------------------------------
 *  Typemap specializations
 * ------------------------------------------------------------ */
%include <pytypemaps.swg>

/* ------------------------------------------------------------
 *  Overloaded operator support
 * ------------------------------------------------------------ */
%include <pyopers.swg>

/* ------------------------------------------------------------
 * Warnings for Python keywords 
 * ------------------------------------------------------------ */
%include <pythonkw.swg>

/* ------------------------------------------------------------
 * The Python autodoc support
 * ------------------------------------------------------------ */
%include <pydocs.swg>

/* ------------------------------------------------------------
 * The Python classes, for C++
 * ------------------------------------------------------------ */
%include <pyclasses.swg>

/* ------------------------------------------------------------
 * The Python initialization function 
 * ------------------------------------------------------------ */
%include <pyinit.swg>


/* ------------------------------------------------------------
 * For backward compatibility
 * ------------------------------------------------------------ */
%include <pybackward.swg>


