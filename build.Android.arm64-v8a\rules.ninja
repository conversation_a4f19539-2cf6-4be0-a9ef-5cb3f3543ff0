# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: RenderDoc
# Configuration: Release
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling C files.

rule C_COMPILER__renderdoc
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out   -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__renderdoc
  command = cmd.exe /C "$PRE_LINK && D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE @$RSP_FILE  && $POST_BUILD"
  description = Linking CXX shared library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__rdoc
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out   -c $in
  description = Building C object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__rdoc
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__rdoc_version
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__renderdoc_libentry
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__renderdoc_libentry
  command = cmd.exe /C "$PRE_LINK && D:\SDKTools\Android\SDK\cmake\3.16.8\bin\cmake.exe -E remove $TARGET_FILE && D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\aarch64-linux-android-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\aarch64-linux-android-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__rdoc_gl
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__rdoc_vulkan
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__rdoc_spirv
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__interceptor_lib
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__rdoc_amd
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__rdoc_intel
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__rdoc_arm
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__renderdoccmd
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out   -c $in
  description = Building C object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__renderdoccmd
  depfile = $DEP_FILE
  deps = gcc
  command = D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__renderdoccmd
  command = cmd.exe /C "$PRE_LINK && D:\SDKTools\Android\SDK\ndk\20.1.5948944\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/sysroot -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = D:\SDKTools\Android\SDK\cmake\3.16.8\bin\cmake.exe -SF:\UGit\renderdoc -BF:\UGit\renderdoc\build.Android.arm64-v8a
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = D:\SDKTools\Android\SDK\cmake\3.22.1\bin\ninja.exe -t clean
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = D:\SDKTools\Android\SDK\cmake\3.22.1\bin\ninja.exe -t targets
  description = All primary targets available:

