<?xml version="1.0"?>
<!-- BEGIN_INCLUDE(manifest) -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="org.renderdoc.renderdoccmd.arm64">

  <uses-sdk android:minSdkVersion="21" android:targetSdkVersion="26"/>

  <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.INTERNET" />

  <!-- Use GL ES 3.2 version -->
  <uses-feature android:glEsVersion="0x00030000" android:required="true" />

  <application android:debuggable="true" android:label="RenderDocCmd" android:icon="@drawable/icon" android:hasCode="true">
    <activity android:name=".Loader" android:label="RenderDoc" android:exported="true" android:screenOrientation="landscape" android:configChanges="orientation|keyboardHidden">
      <meta-data android:name="android.app.lib_name" android:value="renderdoccmd"/>
    </activity>

  </application>
</manifest>
<!-- END_INCLUDE(manifest) -->
