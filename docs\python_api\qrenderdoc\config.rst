API Reference: qrenderdoc Persistant Config
===========================================

This is the API reference for the functions, classes, and enums in the ``qrenderdoc`` module which represents the UI-specific interface for integrating with the UI and writing UI extensions. For more high-level information and instructions on using the python API, see :doc:`../index` and :doc:`../ui_extensions`.

.. contents:: Sections
   :local:

.. currentmodule:: qrenderdoc

Config
------

.. autoclass:: qrenderdoc.PersistantConfig
  :members:

.. autoclass:: qrenderdoc.TimeUnit
  :members:

.. autoclass:: qrenderdoc.OffsetSizeDisplayMode
  :members:
  
.. autofunction:: qrenderdoc.ConfigFilePath
.. autofunction:: qrenderdoc.UnitSuffix
.. autofunction:: qrenderdoc.AddRecentFile
.. autofunction:: qrenderdoc.RemoveRecentFile

Shader Processing
-----------------

.. autoclass:: qrenderdoc.ShaderProcessingTool
  :members:

.. autoclass:: qrenderdoc.ShaderToolOutput
  :members:

Bug Reports
-----------

.. autoclass:: qrenderdoc.BugReport
  :members:

Remote Host
-----------

.. autoclass:: qrenderdoc.RemoteHost
  :members:
