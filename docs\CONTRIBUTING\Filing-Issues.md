# Filing issues

Thanks for taking the time to look at the guidelines for filing issues! I'm happy to help out with your issue, but badly formatted or poorly described bug reports take too much time to untangle so it's your responsibility to give me the best possible chance to know what I need to fix.

:warning: **PLEASE NOTE: YOU MUST USE THE RELEVANT PROVIDED ISSUE TEMPLATE!** :warning:

Using the template lets me know that you know what to include in a well-formatted issue and it shows the needed information at a glance. The template is not complicated and can be filled out easily. If you do not use the template, your issue will be closed.

Please use the bug report template when filing an issue about a bug or a crash, and only use the feature request template when requesting a new feature be added.

This document explains what is expected in the template and is split into two parts - first for bug reports, and second for feature requests.

:warning: :warning: :warning: :warning: :warning: :warning:
As a general rule for all issues: RenderDoc is a tool for debugging your programs, not for copyright infringement. If your problem or request relates to capturing copyrighted applications that are not your own then you will find no help here and your issue will be immediately closed.
:warning: :warning: :warning: :warning: :warning: :warning:

To read more about how to file an issue click the appropriate link below:

1. [Filing a bug](Filing-Issues-Bugs.md)
2. [Requesting a feature](Filing-Issues-Features.md)

