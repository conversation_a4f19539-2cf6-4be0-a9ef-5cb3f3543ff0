/* -----------------------------------------------------------------------------
 *
 *  User interfaces: include these ones as needed
 *
 * ----------------------------------------------------------------------------- */

/* -----------------------------------------------------------------------------
 *  Special types and user helpers 
 * ----------------------------------------------------------------------------- */

argcargv.i		Handler for (int argc, char **argv)
attribute.i		Convert a pair of set/get methods into a "native" python attribute
ccomplex.i		C99 complex type
complex.i		C99 or C++ complex type
cstring.i		Various forms of C character string handling
cwstring.i		Various forms of C wchar_t string handling
embed.i			embedding the Python interpreter in something else
file.i			FILE C type
implicit.i		Allow the use of implicit C++ constructors
wchar.i			wchar_t C type

/* -----------------------------------------------------------------------------
 *  C++ STD + STL
 * ----------------------------------------------------------------------------- */

std_alloc.i		allocator 
std_basic_string.i	basic string
std_char_traits.i	char traits
std_complex.i		complex
std_deque.i		deque	
std_except.i		exceptions
std_ios.i		ios
std_iostream.i		istream/ostream
std_list.i		list
std_map.i		map
std_multimap.i		multimap
std_multiset.i		multiset
std_pair.i		pair
std_set.i		set
std_sstream.i		string stream
std_streambuf.i		streambuf
std_string.i		string
std_vector.i		vector
std_wios.i		wios
std_wiostream.i		wistream/wostream
std_wsstream.i		wstring stream
std_wstreambuf.i	wstreambuf
std_wstring.i		wstring



/* -----------------------------------------------------------------------------
/* 
 *  Implementation files: don't look at them unless you are really drunk
 *
 * ----------------------------------------------------------------------------- */

/* -----------------------------------------------------------------------------
 *  Basic files
 * ----------------------------------------------------------------------------- */

python.swg		Main language file, it just includes what is needed.
pyuserdir.swg		User visible directives (%pythonnondynamic, etc)
pymacros.swg		Internal macros used for typemaps
pyfragments.swg		Allow the user to overload the default fragments
pyopers.swg		Python operations (+=, *=, etc)
pythonkw.swg		Python keywords and special names
pyinit.swg		Python Init method

/* -----------------------------------------------------------------------------
 *  The runtime part
 * ----------------------------------------------------------------------------- */

pyruntime.swg		Main runtime file definition
pyapi.swg		SWIG/Python API declarations
pyrun.swg		Python run-time code 

/* -----------------------------------------------------------------------------
 *  Internal typemap specializations
 * ----------------------------------------------------------------------------- */

pyswigtype.swg		SWIGTYPE
pystrings.swg		Char strings (char *)
pywstrings.swg		Wchar Strings (wchar_t *)
pyprimtypes.swg		Primitive types (shot,int,double,etc)
pycomplex.swg		PyComplex and helper for C/C++ complex types
pydocs.swg		Typemaps documentation

/* -----------------------------------------------------------------------------
 *  C++ STD + STL
 * ----------------------------------------------------------------------------- */

pycontainer.swg		python container iterators
std_common.i		general common code for the STD/STL implementation
std_container.i		general common code for the STD/STL containers


/*-----------------------------------------------------------------------------
 *  Backward compatibility and deprecated
 * ----------------------------------------------------------------------------- */

std_vectora.i		vector + allocator (allocators are now supported in STD/STL)
typemaps.i		old in/out typemaps (doesn't need to be included)
defarg.swg		for processing default arguments with shadow classes
