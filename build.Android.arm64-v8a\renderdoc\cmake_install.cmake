# Install script for directory: F:/UGit/renderdoc/renderdoc

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Program Files (x86)/RenderDoc")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "0")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "TRUE")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE SHARED_LIBRARY FILES "F:/UGit/renderdoc/build.Android.arm64-v8a/lib/libVkLayer_GLES_RenderDoc.so")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/libVkLayer_GLES_RenderDoc.so" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/libVkLayer_GLES_RenderDoc.so")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-strip.exe" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/libVkLayer_GLES_RenderDoc.so")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include" TYPE FILE RENAME "renderdoc_app.h" FILES "F:/UGit/renderdoc/renderdoc/api/app/renderdoc_app.h")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for each subdirectory.
  include("F:/UGit/renderdoc/build.Android.arm64-v8a/renderdoc/driver/gl/cmake_install.cmake")
  include("F:/UGit/renderdoc/build.Android.arm64-v8a/renderdoc/driver/vulkan/cmake_install.cmake")
  include("F:/UGit/renderdoc/build.Android.arm64-v8a/renderdoc/driver/shaders/spirv/cmake_install.cmake")
  include("F:/UGit/renderdoc/build.Android.arm64-v8a/renderdoc/3rdparty/interceptor-lib/cmake_install.cmake")
  include("F:/UGit/renderdoc/build.Android.arm64-v8a/renderdoc/driver/ihv/amd/cmake_install.cmake")
  include("F:/UGit/renderdoc/build.Android.arm64-v8a/renderdoc/driver/ihv/intel/cmake_install.cmake")
  include("F:/UGit/renderdoc/build.Android.arm64-v8a/renderdoc/driver/ihv/arm/cmake_install.cmake")

endif()

