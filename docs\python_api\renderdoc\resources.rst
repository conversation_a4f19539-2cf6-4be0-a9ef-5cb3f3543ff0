API Reference: Resources
========================

This is the API reference for the functions, classes, and enums in the ``renderdoc`` module which represents the underlying interface that the UI is built on top of. For more high-level information and instructions on using the python API, see :doc:`../index`.

.. contents:: Sections
   :local:

.. currentmodule:: renderdoc

General
-------

.. autoclass:: renderdoc.ResourceId
  :members:

.. autoclass:: renderdoc.ResourceDescription
  :members:

.. autoclass:: renderdoc.ResourceType
  :members:

.. autoclass:: renderdoc.DescriptorStoreDescription
  :members:

Textures
--------

.. autoclass:: renderdoc.TextureDescription
  :members:

.. autoclass:: renderdoc.TextureType
  :members:

.. autoclass:: renderdoc.TextureCategory
  :members:

.. autoclass:: renderdoc.Subresource
  :members:

Buffers
-------

.. autoclass:: renderdoc.BufferDescription
  :members:

.. autoclass:: renderdoc.BufferCategory
  :members:
