---
name: Bug report
about: Report a bug or problem encountered while using RenderDoc
---
<!--
⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️ 
If you do not follow the guidelines, or do not use the template below, your issue will be closed with no exceptions!
⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️ 

The template below shows what you need to include in a good bug report, and you MUST use it. More information in the docs:
https://github.com/baldurk/renderdoc/blob/v1.x/docs/CONTRIBUTING/Filing-Issues.md

It is *expressly* forbidden to ask for help with capturing copyrighted programs that you did not create and do not have the source code for.

For example this includes capturing commercial games that you did not create, or capturing Google Maps or Google Earth.

I'm happy to help, but you have to ensure I fully understand what you want and have the information I need. If you're unsure, please read the guide above for full information on what is expected for filing issues.
-->

## Description of Bug

<!-- Here you can enter a description of what you are doing and what bug you are running into. -->
<!-- This is a good time to describe what you want to do, what is actually happening, and what you'd expect to happen instead. -->

## Steps to reproduce

<!-- Please list the steps that someone can take to reproduce the bug. -->

<!-- If you can share your capture or your application, PLEASE DO THAT NOW. It is by far the easiest way to demonstrate a bug. You can share it privately via <NAME_EMAIL> and mention it here. -->
<!-- If you cannot share because of privacy or other reasons, please state that and give as much extra information as you can. -->
<!-- Steps like "run my application" or "load this capture" are not useful unless you share the application or capture. Be specific! -->

## Environment

<!-- if you are running a nightly build, list the date or commit hash for the version -->

* RenderDoc version: XXX
* Operating System: XXX
* Graphics API: XXX

<!-- More details here never hurt! For example your GPU, driver version, etc. -->
