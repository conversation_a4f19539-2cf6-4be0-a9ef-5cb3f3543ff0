API Reference: Vulkan Pipeline State
=====================================

This is the API reference for the functions, classes, and enums in the ``renderdoc`` module which represents the underlying interface that the UI is built on top of. For more high-level information and instructions on using the python API, see :doc:`../../index`.

.. contents:: Sections
   :local:

.. currentmodule:: renderdoc

.. autoclass:: VKState
  :members:

Pipeline and Bindings
---------------------

.. autoclass:: VKPipeline
  :members:

.. autoclass:: VKDescriptorSet
  :members:

.. autoclass:: VKDynamicOffset
  :members:

Vertex Input
------------

.. autoclass:: VKInputAssembly
  :members:

.. autoclass:: VKIndexBuffer
  :members:

.. autoclass:: VKVertexInput
  :members:

.. autoclass:: VKVertexAttribute
  :members:

.. autoclass:: VKVertexBinding
  :members:

.. autoclass:: VKVertexBuffer
  :members:

Shader
------

.. autoclass:: VKShader
  :members:

Tessellation
------------

.. autoclass:: VKTessellation
  :members:

Transform Feedback
------------------

.. autoclass:: VKTransformFeedback
  :members:

.. autoclass:: VKXFBBuffer
  :members:

Rasterizer
----------

.. autoclass:: VKViewportScissor
  :members:

.. autoclass:: VKViewState
  :members:

.. autoclass:: VKRasterizer
  :members:

Multisampling
-------------

.. autoclass:: VKMultiSample
  :members:

.. autoclass:: VKSampleLocations
  :members:

Blending
--------

.. autoclass:: VKColorBlendState
  :members:

Depth/Stencil State
-------------------

.. autoclass:: VKDepthStencil
  :members:

Renderpass and Framebuffer
--------------------------

.. autoclass:: VKCurrentPass
  :members:

.. autoclass:: VKRenderPass
  :members:

.. autoclass:: VKFramebuffer
  :members:

.. autoclass:: VKRenderArea
  :members:

Image States
------------

.. autoclass:: VKImageData
  :members:

.. autoclass:: VKImageLayout
  :members:

Conditional Rendering
---------------------

.. autoclass:: VKConditionalRendering
  :members:

