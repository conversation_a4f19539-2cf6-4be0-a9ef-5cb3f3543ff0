API Reference: OpenGL Pipeline State
=====================================

This is the API reference for the functions, classes, and enums in the ``renderdoc`` module which represents the underlying interface that the UI is built on top of. For more high-level information and instructions on using the python API, see :doc:`../../index`.

.. contents:: Sections
   :local:

.. currentmodule:: renderdoc

.. autoclass:: GLState
  :members:

Vertex Input
------------

.. autoclass:: GLVertexInput
  :members:

.. autoclass:: GLVertexAttribute
  :members:

.. autoclass:: GLVertexBuffer
  :members:

Shader
------

.. autoclass:: GLShader
  :members:

.. autoclass:: GLTextureCompleteness
  :members:

Fixed Vertex Processing
-----------------------

.. autoclass:: GLFixedVertexProcessing
  :members:

Transform Feedback
------------------

.. autoclass:: GLFeedback
  :members:

Rasterizer
----------

.. autoclass:: GLRasterizer
  :members:

.. autoclass:: GLRasterizerState
  :members:

Depth/Stencil State
-------------------

.. autoclass:: GLDepthState
  :members:

.. autoclass:: GLStencilState
  :members:

Framebuffer
-----------

.. autoclass:: GLFrameBuffer
  :members:

.. autoclass:: GLFBO
  :members:

Blending
--------

.. autoclass:: GLBlendState
  :members:

Hints
-----

.. autoclass:: GLHints
  :members:

.. autoclass:: QualityHint
  :members:
