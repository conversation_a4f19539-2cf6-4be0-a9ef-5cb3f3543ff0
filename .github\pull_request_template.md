<!--
Before submitting a pull request you are strongly recommended to read the
docs/CONTRIBUTING.md file which gives some information on how to prepare a
change:

https://github.com/baldurk/renderdoc/blob/v1.x/docs/CONTRIBUTING.md

For small changes you don't have to read the document end to end, but should at
least look at the sections on how to ensure your code and commits are formatted
according to the style requirements.
-->

## Description

<!--
Describe here what your pull request changes and why it should happen. For small
changes which are obvious this can just be a line or two - even the commit
message is sometimes enough.
-->
