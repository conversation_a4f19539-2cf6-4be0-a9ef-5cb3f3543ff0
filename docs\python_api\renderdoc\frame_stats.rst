API Reference: Frame Statistics
===============================

This is the API reference for the functions, classes, and enums in the ``renderdoc`` module which represents the underlying interface that the UI is built on top of. For more high-level information and instructions on using the python API, see :doc:`../index`.

.. contents:: Sections
   :local:

.. currentmodule:: renderdoc

.. autoclass:: renderdoc.FrameStatistics
  :members:

Resource Statistics
-------------------

.. autoclass:: renderdoc.ResourceUpdateStats
  :members:

.. autoclass:: renderdoc.BucketRecordType
  :members:

Drawcall Statistics
-------------------

.. autoclass:: renderdoc.DrawcallStats
  :members:

.. autoclass:: renderdoc.DispatchStats
  :members:

Shader Statistics
-----------------

.. autoclass:: renderdoc.ConstantBindStats
  :members:

.. autoclass:: renderdoc.SamplerBindStats
  :members:

.. autoclass:: renderdoc.ResourceBindStats
  :members:

.. autoclass:: renderdoc.ShaderChangeStats
  :members:
  
Fixed Function Statistics
-------------------------

.. autoclass:: renderdoc.IndexBindStats
  :members:

.. autoclass:: renderdoc.VertexBindStats
  :members:

.. autoclass:: renderdoc.LayoutBindStats
  :members:

.. autoclass:: renderdoc.BlendStats
  :members:

.. autoclass:: renderdoc.DepthStencilStats
  :members:

.. autoclass:: renderdoc.RasterizationStats
  :members:

.. autoclass:: renderdoc.OutputTargetStats
  :members:

