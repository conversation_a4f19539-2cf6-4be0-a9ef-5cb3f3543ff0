SWIG Copyright and Authors
--------------------------

Copyright (c) 1995-2011 The SWIG Developers
Copyright (c) 2005-2006 Arizona Board of Regents (University of Arizona).
Copyright (c) 1998-2005 University of Chicago.
Copyright (c) 1995-1998 The University of Utah and the Regents of the University of California

Portions also copyrighted by:
 Network Applied Communication Laboratory, Inc
 Information-technology Promotion Agency, Japan

Active SWIG Developers:
 <PERSON> (<EMAIL>)                 (SWIG core, Java, C#, Windows, Cygwin)
 <PERSON><PERSON> (<EMAIL>)                             (PHP)
 <PERSON> (<EMAIL>)                         (R)
 <PERSON> (<EMAIL>)              (Octave)
 <PERSON> (<EMAIL>)                    (D)
 <PERSON> (<EMAIL>)              (Javascript)
 <PERSON><PERSON><PERSON> (<EMAIL>)                     (Javascript)
 <PERSON> (<EMAIL>) (Scilab)

Past SWIG developers and major contributors include:
 <PERSON> (<EMAIL>)                      (SWIG core, <PERSON>, <PERSON>c<PERSON>, <PERSON><PERSON>)
 <PERSON><PERSON> (<EMAIL>)          (Modula3)
 <PERSON> (<EMAIL>)      (Guile, MzScheme)
 Luigi Ballabio (<EMAIL>)            (STL wrapping)
 Mikel Bancroft (<EMAIL>)                         (Allegro CL)
 Surendra Singhi (<EMAIL>)                 (CLISP, CFFI)
 Marcelo Matus (<EMAIL>)                  (SWIG core, Python, UTL[python,perl,tcl,ruby])
 Art Yerkes (<EMAIL>)                       (Ocaml)
 Lyle Johnson (<EMAIL>)                (Ruby)
 Charlie Savage (<EMAIL>)                      (Ruby)
 Thien-Thi Nguyen (<EMAIL>)                          (build/test/misc)
 Richard Palmer (<EMAIL>)                  (PHP)
 Sam Liddicott - Ananova Ltd (<EMAIL>)         (PHP)
 Tim Hockin - Sun Microsystems (<EMAIL>)          (PHP)
 Kevin Ruland                                             (PHP)
 Shibukawa Yoshiki                                        (Japanese Translation)
 Jason Stewart (<EMAIL>)                (Perl5)
 Loic Dachary                                             (Perl5)
 David Fletcher                                           (Perl5)
 Gary Holt                                                (Perl5)
 Masaki Fukushima                                         (Ruby)
 Scott Michel (<EMAIL>)                        (Java directors)
 Tiger Feng (<EMAIL>)                    (SWIG core)
 Mark Rose (<EMAIL>)                            (Directors)
 Jonah Beckford (<EMAIL>)                   (CHICKEN)
 Ahmon Dancy (<EMAIL>)                            (Allegro CL)
 Dirk Gerrits                                             (Allegro CL)
 Neil Cawse                                               (C#)
 Harco de Hilster                                         (Java)
 Alexey Dyachenko (<EMAIL>)                  (Tcl)
 Bob Techentin                                            (Tcl)
 Martin Froehlich <<EMAIL>>               (Guile)
 Marcio Luis Teixeira <<EMAIL>>       (Guile)
 Duncan Temple Lang                                       (R)
 Miklos Vajna <<EMAIL>>                    (PHP directors)
 Mark Gossage (<EMAIL>)                      (Lua)
 Raman Gopalan (<EMAIL>)                   (eLua)
 Gonzalo Garramuno (<EMAIL>)             (Ruby, Ruby's UTL)
 John Lenz                                                (Guile, MzScheme updates, Chicken module, runtime system)
 Baozeng Ding  <<EMAIL>>                        (Scilab)
 Ian Lance Taylor                                         (Go)
 Vadim Zeitlin                                            (PCRE, Python)
 Stefan Zager (<EMAIL>)                          (Python)
 Vincent Couvert                                          (Scilab)
 Sylvestre Ledru                                          (Scilab)
 Wolfgang Frisch                                          (Scilab)

Past contributors include:
 James Michael DuPont, Clark McGrew, Dustin Mitchell, Ian Cooke, Catalin Dumitrescu, Baran
 Kovuk, Oleg Tolmatcev, Tal Shalif, Lluis Padro, Chris Seatory, Igor Bely, Robin Dunn,
 Edward Zimmermann, David Ascher, Dominique Dumont, Pier Giorgio Esposito, Hasan Baran Kovuk,
 Klaus Wiederänders, Richard Beare, Hans Oesterholt.
 (See CHANGES and CHANGES.current and the bug tracker for a more complete list).

Past students:
 Songyan Feng (Chicago).
 Xinghua Shi (Chicago).
 Jing Cao (Chicago).
 Aquinas Hobor (Chicago).

Historically, the following people contributed to early versions of SWIG.
Peter Lomdahl, Brad Holian, Shujia Zhou, Niels Jensen, and Tim Germann
at Los Alamos National Laboratory were the first users. Patrick
Tullmann at the University of Utah suggested the idea of automatic
documentation generation. John Schmidt and Kurtis Bleeker at the
University of Utah tested out the early versions.  Chris Johnson
supported SWIG's developed at the University of Utah. John Buckman,
Larry Virden, and Tom Schwaller provided valuable input on the first
releases and improving the portability of SWIG. David Fletcher and
Gary Holt have provided a great deal of input on improving SWIG's
Perl5 implementation. Kevin Butler contributed the first Windows NT
port.

Early bug reports and patches:
Adam Hupp, Arthur Smyles, Brad Clements, Brett Williams, Buck Hodges,
Burkhard Kloss, Chia-Liang Kao, Craig Files, Dennis Marsa, Dieter Baron,
Drake Diedrich, Fleur Diana Dragan, Gary Pennington, Geoffrey Hort, Gerald Williams,
Greg Anderson, Greg Kochanski, Greg Troxel, Henry Rowley, Irina Kotlova,
Israel Taller, James Bailey, Jim Fulton, Joel Reed, Jon Travis,
Junio Hamano, Justin Heyes-Jones, Karl Forner, Keith Davidson,
Krzysztof Kozminski, Larry Virden, Luke J Crook, Magnus Ljung, Marc Zonzon,
Mark Howson, Micahel Scharf, Michel Sanner, Mike Romberg, Mike Simons,
Mike Weiblen, Paul Brannan, Ram Bhamidipaty, Reinhard Fobbe, Rich Wales,
Richard Salz, Roy Lecates, Rudy Albachten, Scott Drummonds
Scott Michel, Shaun Lowry, Steve Galser, Tarn Weisner Burton,
Thomas Weidner, Tony Seward, Uwe Steinmann, Vadim Chugunov, Wyss Clemens,
Zhong Ren.

