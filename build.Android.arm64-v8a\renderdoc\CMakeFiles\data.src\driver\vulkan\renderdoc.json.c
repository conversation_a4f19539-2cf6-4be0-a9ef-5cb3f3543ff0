unsigned char driver_vulkan_renderdoc_json[] = {
  0x7b, 0x0d, 0x0a, 0x20, 0x20, 0x22, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x66,
  0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
  0x6e, 0x22, 0x20, 0x3a, 0x20, 0x22, 0x31, 0x2e, 0x31, 0x2e, 0x32, 0x22,
  0x2c, 0x0d, 0x0a, 0x20, 0x20, 0x22, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x22,
  0x20, 0x3a, 0x20, 0x7b, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x22, 0x6e,
  0x61, 0x6d, 0x65, 0x22, 0x3a, 0x20, 0x22, 0x56, 0x4b, 0x5f, 0x4c, 0x41,
  0x59, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x44, 0x4f,
  0x43, 0x5f, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x22, 0x2c, 0x0d,
  0x0a, 0x20, 0x20, 0x20, 0x20, 0x22, 0x74, 0x79, 0x70, 0x65, 0x22, 0x3a,
  0x20, 0x22, 0x47, 0x4c, 0x4f, 0x42, 0x41, 0x4c, 0x22, 0x2c, 0x0d, 0x0a,
  0x20, 0x20, 0x20, 0x20, 0x22, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
  0x5f, 0x70, 0x61, 0x74, 0x68, 0x22, 0x3a, 0x20, 0x22, 0x40, 0x56, 0x55,
  0x4c, 0x4b, 0x41, 0x4e, 0x5f, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x4d,
  0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x54, 0x48, 0x40, 0x22,
  0x2c, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x22, 0x61, 0x70, 0x69, 0x5f,
  0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x3a, 0x20, 0x22, 0x31,
  0x2e, 0x33, 0x2e, 0x31, 0x33, 0x31, 0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20,
  0x20, 0x20, 0x22, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
  0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
  0x6e, 0x22, 0x3a, 0x20, 0x22, 0x40, 0x52, 0x45, 0x4e, 0x44, 0x45, 0x52,
  0x44, 0x4f, 0x43, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f,
  0x4d, 0x49, 0x4e, 0x4f, 0x52, 0x40, 0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20,
  0x20, 0x20, 0x22, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
  0x6f, 0x6e, 0x22, 0x3a, 0x20, 0x22, 0x44, 0x65, 0x62, 0x75, 0x67, 0x67,
  0x69, 0x6e, 0x67, 0x20, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x20,
  0x6c, 0x61, 0x79, 0x65, 0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x52, 0x65,
  0x6e, 0x64, 0x65, 0x72, 0x44, 0x6f, 0x63, 0x22, 0x2c, 0x0d, 0x0a, 0x20,
  0x20, 0x20, 0x20, 0x22, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
  0x73, 0x22, 0x3a, 0x20, 0x7b, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x22, 0x76, 0x6b, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61,
  0x6e, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x41, 0x64, 0x64, 0x72, 0x22,
  0x3a, 0x20, 0x22, 0x56, 0x4b, 0x5f, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f,
  0x52, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x44, 0x4f, 0x43, 0x5f, 0x43, 0x61,
  0x70, 0x74, 0x75, 0x72, 0x65, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74,
  0x61, 0x6e, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x41, 0x64, 0x64, 0x72,
  0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x22, 0x76,
  0x6b, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
  0x6f, 0x63, 0x41, 0x64, 0x64, 0x72, 0x22, 0x3a, 0x20, 0x22, 0x56, 0x4b,
  0x5f, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x4e, 0x44, 0x45,
  0x52, 0x44, 0x4f, 0x43, 0x5f, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
  0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f,
  0x63, 0x41, 0x64, 0x64, 0x72, 0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x22, 0x76, 0x6b, 0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69,
  0x61, 0x74, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x79,
  0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x56,
  0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x3a, 0x20, 0x22, 0x56, 0x4b,
  0x5f, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x4e, 0x44, 0x45,
  0x52, 0x44, 0x4f, 0x43, 0x5f, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
  0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x61,
  0x64, 0x65, 0x72, 0x4c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65,
  0x72, 0x66, 0x61, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
  0x22, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x7d, 0x2c, 0x0d, 0x0a, 0x20,
  0x20, 0x20, 0x20, 0x22, 0x70, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x74,
  0x61, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
  0x6e, 0x73, 0x22, 0x3a, 0x20, 0x7b, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x22, 0x76, 0x6b, 0x45, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x61,
  0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x78,
  0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x70, 0x65,
  0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x3a, 0x20, 0x22, 0x56, 0x4b, 0x5f,
  0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x4e, 0x44, 0x45, 0x52,
  0x44, 0x4f, 0x43, 0x5f, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x45,
  0x6e, 0x75, 0x6d, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74,
  0x61, 0x6e, 0x63, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
  0x6e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22,
  0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x7d, 0x2c, 0x0d, 0x0a, 0x20, 0x20,
  0x20, 0x20, 0x22, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
  0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x3a,
  0x20, 0x5b, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x7b, 0x0d,
  0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x22, 0x6e, 0x61,
  0x6d, 0x65, 0x22, 0x3a, 0x20, 0x22, 0x56, 0x4b, 0x5f, 0x45, 0x58, 0x54,
  0x5f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x73,
  0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x22, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
  0x6e, 0x22, 0x3a, 0x20, 0x22, 0x31, 0x22, 0x0d, 0x0a, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x7d, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x5d, 0x2c,
  0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x22, 0x64, 0x65, 0x76, 0x69, 0x63,
  0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
  0x22, 0x3a, 0x20, 0x5b, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x7b, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x22,
  0x6e, 0x61, 0x6d, 0x65, 0x22, 0x3a, 0x20, 0x22, 0x56, 0x4b, 0x5f, 0x45,
  0x58, 0x54, 0x5f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x6d, 0x61, 0x72,
  0x6b, 0x65, 0x72, 0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x22, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x76, 0x65, 0x72,
  0x73, 0x69, 0x6f, 0x6e, 0x22, 0x3a, 0x20, 0x22, 0x34, 0x22, 0x2c, 0x0d,
  0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x22, 0x65, 0x6e,
  0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x3a, 0x20,
  0x5b, 0x22, 0x76, 0x6b, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x61, 0x72,
  0x6b, 0x65, 0x72, 0x53, 0x65, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
  0x54, 0x61, 0x67, 0x45, 0x58, 0x54, 0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x22, 0x76,
  0x6b, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x72,
  0x53, 0x65, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d,
  0x65, 0x45, 0x58, 0x54, 0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x22, 0x76, 0x6b, 0x43,
  0x6d, 0x64, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x61, 0x72, 0x6b, 0x65,
  0x72, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x45, 0x58, 0x54, 0x22, 0x2c, 0x0d,
  0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x22, 0x76, 0x6b, 0x43, 0x6d, 0x64, 0x44, 0x65, 0x62, 0x75, 0x67,
  0x4d, 0x61, 0x72, 0x6b, 0x65, 0x72, 0x45, 0x6e, 0x64, 0x45, 0x58, 0x54,
  0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x22, 0x76, 0x6b, 0x43, 0x6d, 0x64, 0x44, 0x65,
  0x62, 0x75, 0x67, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x73,
  0x65, 0x72, 0x74, 0x45, 0x58, 0x54, 0x22, 0x0d, 0x0a, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x5d, 0x0d, 0x0a, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x7d, 0x2c, 0x0d, 0x0a, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x7b, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x22, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x3a, 0x20, 0x22, 0x56,
  0x4b, 0x5f, 0x45, 0x58, 0x54, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x69, 0x6e,
  0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x22, 0x73, 0x70, 0x65, 0x63, 0x5f,
  0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x3a, 0x20, 0x22, 0x31,
  0x22, 0x2c, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x22, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
  0x22, 0x3a, 0x20, 0x5b, 0x22, 0x76, 0x6b, 0x47, 0x65, 0x74, 0x50, 0x68,
  0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
  0x54, 0x6f, 0x6f, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
  0x65, 0x73, 0x45, 0x58, 0x54, 0x22, 0x5d, 0x0d, 0x0a, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x7d, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x5d, 0x2c,
  0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x22, 0x65, 0x6e, 0x61, 0x62, 0x6c,
  0x65, 0x5f, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e,
  0x74, 0x22, 0x3a, 0x20, 0x7b, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x22, 0x40, 0x56, 0x55, 0x4c, 0x4b, 0x41, 0x4e, 0x5f, 0x45, 0x4e,
  0x41, 0x42, 0x4c, 0x45, 0x5f, 0x56, 0x41, 0x52, 0x40, 0x22, 0x3a, 0x20,
  0x22, 0x31, 0x22, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x7d, 0x2c, 0x0d,
  0x0a, 0x20, 0x20, 0x20, 0x20, 0x22, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
  0x65, 0x5f, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e,
  0x74, 0x22, 0x3a, 0x20, 0x7b, 0x0d, 0x0a, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x22, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x56, 0x55,
  0x4c, 0x4b, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x44,
  0x4f, 0x43, 0x5f, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x40,
  0x52, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x44, 0x4f, 0x43, 0x5f, 0x56, 0x45,
  0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x4a, 0x4f, 0x52, 0x40,
  0x5f, 0x40, 0x52, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x44, 0x4f, 0x43, 0x5f,
  0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x49, 0x4e, 0x4f,
  0x52, 0x40, 0x22, 0x3a, 0x20, 0x22, 0x31, 0x22, 0x0d, 0x0a, 0x20, 0x20,
  0x20, 0x20, 0x7d, 0x0d, 0x0a, 0x20, 0x20, 0x7d, 0x0d, 0x0a, 0x7d, 0x0d,
  0x0a
};
unsigned int driver_vulkan_renderdoc_json_len = 1693;
