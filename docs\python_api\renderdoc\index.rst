renderdoc python module
=======================

This is the API reference for the functions, classes, and enums in the ``renderdoc`` module which represents the underlying interface that the UI is built on top of. For more high-level information and instructions on using the python API, see :doc:`../index`.

.. toctree::
	:hidden:

	capturing
	replay
	outputs
	analysis
	formats
	resources
	shaders
	pipelines/index
	structured_data
	counters
	frame_stats
	utils

* :doc:`capturing`
* :doc:`replay`
* :doc:`outputs`
* :doc:`analysis`
* :doc:`formats`
* :doc:`resources`
* :doc:`shaders`
* :doc:`pipelines/index`
* :doc:`structured_data`
* :doc:`counters`
* :doc:`frame_stats`
* :doc:`utils`
