# English translations for sphinx_rtd_theme.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 0.4.3.dev0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2021-01-04 13:48-0800\n"
"PO-Revision-Date: 2019-07-16 21:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Swedish (https://www.transifex.com/readthedocs/teams/101354/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: sphinx_rtd_theme/breadcrumbs.html:45 sphinx_rtd_theme/breadcrumbs.html:47
msgid "Edit on GitHub"
msgstr "Editera på GitHub"

#: sphinx_rtd_theme/breadcrumbs.html:52 sphinx_rtd_theme/breadcrumbs.html:54
msgid "Edit on Bitbucket"
msgstr "Editera på Bitbucket"

#: sphinx_rtd_theme/breadcrumbs.html:59 sphinx_rtd_theme/breadcrumbs.html:61
msgid "Edit on GitLab"
msgstr "Editera på GitLab"

#: sphinx_rtd_theme/breadcrumbs.html:64 sphinx_rtd_theme/breadcrumbs.html:66
msgid "View page source"
msgstr "Visa sidkälla"

#: sphinx_rtd_theme/breadcrumbs.html:76 sphinx_rtd_theme/footer.html:5
msgid "Next"
msgstr "Nästa"

#: sphinx_rtd_theme/breadcrumbs.html:79 sphinx_rtd_theme/footer.html:8
msgid "Previous"
msgstr "Tillbaka"

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:29
msgid "Build"
msgstr "Bygg"

#. the phrase "revision" comes from Git, referring to a commit
#: sphinx_rtd_theme/footer.html:35
msgid "Revision"
msgstr "Ändra"

#: sphinx_rtd_theme/footer.html:40
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Senast uppdaterad %(last_updated)s."

#. the variable "sphinx_web" is a link to the Sphinx project documentation
#. with
#. the text "Sphinx"
#: sphinx_rtd_theme/footer.html:52
#, python-format
msgid "Built with %(sphinx_web)s using a"
msgstr "Gjord med %(sphinx_web)s med hjälp av"

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documenation
#: sphinx_rtd_theme/footer.html:54
msgid "theme"
msgstr "tema"

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:56
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr "erhållet av %(readthedocs_web)s"

#: sphinx_rtd_theme/layout.html:85
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Sök i %(docstitle)s"

#: sphinx_rtd_theme/layout.html:93
msgid "About these documents"
msgstr "Om dessa dokument"

#: sphinx_rtd_theme/layout.html:96
msgid "Index"
msgstr "Index"

#: sphinx_rtd_theme/layout.html:99 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr "Sök"

#: sphinx_rtd_theme/layout.html:102
msgid "Copyright"
msgstr "Upphovsrätt"

#: sphinx_rtd_theme/layout.html:134
msgid "Logo"
msgstr "Logo"

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr ""
"Var vänlig och aktivera JavaScript för att möjliggöra sökfunktionaliteten."

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr "Sökresultat"

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""
"Din sökning gav inga träffar. Var vänlig och se till att alla ord är rätt "
"stavade och att du har valt tillräckligt många kategorier."

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr "Sök i dokumentationen"

#: sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr "Versioner"

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr "Nerladdningar"

#. The phrase "Read the Docs" is not translated
#: sphinx_rtd_theme/versions.html:24
msgid "On Read the Docs"
msgstr "På Read the Docs"

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr "Projekt Hem"

#~ msgid "Docs"
#~ msgstr "Dokumentation"

#~ msgid "Free document hosting provided by"
#~ msgstr "Gratis dokumentations hysning erhållen av"

#~ msgid "Documentation Home"
#~ msgstr "Dokumentation Hem"
