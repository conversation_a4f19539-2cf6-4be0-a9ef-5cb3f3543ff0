---
name: Feature request
about: Suggest an improvement or new feature be added to RenderDoc
---
<!--
⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️ 
IF YOU DO NOT FOLLOW THE GUIDELINES, OR DO NOT USE THE TEMPLATE BELOW, YOUR ISSUE WILL BE CLOSED! NO EXCEPTIONS!
⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️ 

The template below shows what you need to include in a good feature request, and you MUST use it. More information in the docs:
https://github.com/baldurk/renderdoc/blob/v1.x/docs/CONTRIBUTING/Filing-Issues.md

I'm happy to help, but you have to ensure I fully understand what you want and have the information I need. If you're unsure, please read the guide above for full information on what is expected for filing issues.
-->

## Description of Feature

<!-- Here you should not just describe what feature you want. Please describe the context of what you are trying to do or what workflow you would like, and why you can't do that with RenderDoc today. -->

<!-- You can then separately describe a specific feature or solution you'd like to see to address that desire. -->

## Environment

<!-- if you are running a nightly build, list the date or commit hash for the version -->

* RenderDoc version: XXX
* Operating System: XXX
* Graphics API: XXX

<!-- You should still list the details here so that the scope of the request can be understood. -->
