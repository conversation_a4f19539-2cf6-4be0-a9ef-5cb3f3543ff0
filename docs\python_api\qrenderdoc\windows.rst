API Reference: qrenderdoc Windows
=================================

This is the API reference for the functions, classes, and enums in the ``qrenderdoc`` module which represents the UI-specific interface for integrating with the UI and writing UI extensions. For more high-level information and instructions on using the python API, see :doc:`../index` and :doc:`../ui_extensions`.

.. contents:: Sections
   :local:

.. currentmodule:: qrenderdoc

Main Window
-----------

.. autoclass:: qrenderdoc.MainWindow
  :members:

Event Browser
-------------

.. autoclass:: qrenderdoc.EventBrowser
  :members:

API Inspector
-------------

.. autoclass:: qrenderdoc.APIInspector
  :members:

Pipeline State
--------------

.. autoclass:: qrenderdoc.PipelineStage
  :members:
  :exclude-members: enum_constants__, 

.. autoclass:: qrenderdoc.PipelineStateViewer
  :members:

Descriptor Viewer
-----------------

.. autoclass:: qrenderdoc.DescriptorViewer
  :members:

Texture Viewer
--------------

.. autoclass:: qrenderdoc.FollowType
  :members:
  :exclude-members: enum_constants__, 

.. autoclass:: qrenderdoc.TextureViewer
  :members:

Buffer Viewer
-------------

.. autoclass:: qrenderdoc.BufferViewer
  :members:

Resource Inspector
------------------

.. autoclass:: qrenderdoc.ResourceInspector
  :members:

Capture Dialog
--------------

.. autoclass:: qrenderdoc.CaptureDialog
  :members:

Debug Messages
--------------

.. autoclass:: qrenderdoc.DebugMessageView
  :members:

Diagnostic Log
--------------

.. autoclass:: qrenderdoc.DiagnosticLogView
  :members:

Comment View
------------

.. autoclass:: qrenderdoc.CommentView
  :members:

Statistics Viewer
-----------------

.. autoclass:: qrenderdoc.StatisticsViewer
  :members:

Timeline Bar
------------

.. autoclass:: qrenderdoc.TimelineBar
  :members:

Performance Counter Viewer
--------------------------

.. autoclass:: qrenderdoc.PerformanceCounterViewer
  :members:

Python Shell
------------

.. autoclass:: qrenderdoc.PythonShell
  :members:

Shader Viewer
-------------

.. autoclass:: qrenderdoc.ShaderViewer
  :members:

Pixel History
-------------

.. autoclass:: qrenderdoc.PixelHistoryView
  :members:


Shader Message Viewer
---------------------

.. autoclass:: qrenderdoc.ShaderMessageViewer
  :members:

