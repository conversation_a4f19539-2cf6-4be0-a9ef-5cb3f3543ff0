# English translations for sphinx_rtd_theme.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 0.4.3.dev0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2021-01-04 13:48-0800\n"
"PO-Revision-Date: 2019-07-16 21:44+0000\n"
"Last-Translator: lvv83 <<EMAIL>>, 2019\n"
"Language-Team: Russian (https://www.transifex.com/readthedocs/teams/101354/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#: sphinx_rtd_theme/breadcrumbs.html:45 sphinx_rtd_theme/breadcrumbs.html:47
msgid "Edit on GitHub"
msgstr "Редактировать на GitHub"

#: sphinx_rtd_theme/breadcrumbs.html:52 sphinx_rtd_theme/breadcrumbs.html:54
msgid "Edit on Bitbucket"
msgstr "Редактировать на BitBucket"

#: sphinx_rtd_theme/breadcrumbs.html:59 sphinx_rtd_theme/breadcrumbs.html:61
msgid "Edit on GitLab"
msgstr "Редактировать на GitLab"

#: sphinx_rtd_theme/breadcrumbs.html:64 sphinx_rtd_theme/breadcrumbs.html:66
msgid "View page source"
msgstr "Просмотреть исходный код страницы"

#: sphinx_rtd_theme/breadcrumbs.html:76 sphinx_rtd_theme/footer.html:5
msgid "Next"
msgstr "Следующая"

#: sphinx_rtd_theme/breadcrumbs.html:79 sphinx_rtd_theme/footer.html:8
msgid "Previous"
msgstr "Предыдущая"

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:29
msgid "Build"
msgstr "Сборка"

#. the phrase "revision" comes from Git, referring to a commit
#: sphinx_rtd_theme/footer.html:35
msgid "Revision"
msgstr "Ревизия"

#: sphinx_rtd_theme/footer.html:40
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Последний раз обновлено %(last_updated)s."

#. the variable "sphinx_web" is a link to the Sphinx project documentation
#. with
#. the text "Sphinx"
#: sphinx_rtd_theme/footer.html:52
#, python-format
msgid "Built with %(sphinx_web)s using a"
msgstr "Собрано при помощи %(sphinx_web)s с использованием"

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documenation
#: sphinx_rtd_theme/footer.html:54
msgid "theme"
msgstr "темы,"

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:56
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr "предоставленной %(readthedocs_web)s"

#: sphinx_rtd_theme/layout.html:85
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Поиск в %(docstitle)s"

#: sphinx_rtd_theme/layout.html:93
msgid "About these documents"
msgstr "Об этих документах"

#: sphinx_rtd_theme/layout.html:96
msgid "Index"
msgstr "Алфавитный указатель"

#: sphinx_rtd_theme/layout.html:99 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr "Поиск"

#: sphinx_rtd_theme/layout.html:102
msgid "Copyright"
msgstr "Авторские права"

#: sphinx_rtd_theme/layout.html:134
msgid "Logo"
msgstr "Логотип"

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr "Активируйте JavaScript, чтобы использовать функционал поиска."

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr "Результаты поиска"

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""
"По Вашему запросу не найдено результатов. Пожалуйста, проверьте, что все "
"слова написаны правильно, и Вы выбрали нужные категории."

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr "Поиск в документации"

#: sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr "Версии"

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr "Загрузки"

#. The phrase "Read the Docs" is not translated
#: sphinx_rtd_theme/versions.html:24
msgid "On Read the Docs"
msgstr "На Read the Docs"

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr "Домашняя страница проекта"

#: sphinx_rtd_theme/versions.html:29
msgid "Builds"
msgstr "Сборки"

#~ msgid "Docs"
#~ msgstr "Документация"

#~ msgid "Free document hosting provided by"
#~ msgstr "Бесплатный хостинг документов, предоставленный"
