API Reference: Formats
======================

This is the API reference for the functions, classes, and enums in the ``renderdoc`` module which represents the underlying interface that the UI is built on top of. For more high-level information and instructions on using the python API, see :doc:`../index`.

.. contents:: Sections
   :local:

.. currentmodule:: renderdoc

Resource Format
---------------

.. autoclass:: renderdoc.ResourceFormat
  :members:

.. autoclass:: renderdoc.ResourceFormatType
  :members:

.. autoclass:: renderdoc.CompType
  :members:

