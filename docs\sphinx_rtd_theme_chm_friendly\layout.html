{# TEMPLATE VAR SETTINGS #}
{%- set url_root = pathto('', 1) %}
{%- if url_root == '#' %}{% set url_root = '' %}{% endif %}
{%- if not embedded and docstitle %}
  {%- set titlesuffix = " &mdash; "|safe + docstitle|e %}
{%- else %}
  {%- set titlesuffix = "" %}
{%- endif %}
{%- set lang_attr = 'en' if language == None else (language | replace('_', '-')) %}
{%- set sphinx_writer = 'writer-html5' if html5_doctype else 'writer-html4' %}

{# Build sphinx_version_info tuple from sphinx_version string in pure Jinja #}
{%- set (_ver_major, _ver_minor) = (sphinx_version.split('.') | list)[:2] | map('int') -%}
{%- set sphinx_version_info = (_ver_major, _ver_minor, -1) -%}

<!DOCTYPE html>
<html class="{{ sphinx_writer }}" lang="{{ lang_attr }}" >
<head>
  {% if 'htmlhelp' not in builder %}
  <meta charset="utf-8" />
  {% endif %}
  {{ metatags }}
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  {% block htmltitle %}
  <title>{{ title|striptags|e }}{{ titlesuffix }}</title>
  {% endblock %}

  {# CSS #}
  {%- if sphinx_version_info < (4, 0) -%}
    <link rel="stylesheet" href="{{ pathto('_static/' + style, 1) }}" type="text/css" />
    <link rel="stylesheet" href="{{ pathto('_static/pygments.css', 1) }}" type="text/css" />
  {%- endif %}
  {%- for css in css_files %}
    {%- if css|attr("rel") %}
  <link rel="{{ css.rel }}" href="{{ pathto(css.filename, 1) }}" type="text/css"{% if css.title is not none %} title="{{ css.title }}"{% endif %} />
    {%- else %}
  <link rel="stylesheet" href="{{ pathto(css, 1) }}" type="text/css" />
    {%- endif %}
  {%- endfor %}

  {%- for cssfile in extra_css_files %}
    <link rel="stylesheet" href="{{ pathto(cssfile, 1) }}" type="text/css" />
  {%- endfor %}

  {# FAVICON #}
  {% if favicon %}
    <link rel="shortcut icon" href="{{ pathto('_static/' + favicon, 1) }}"/>
  {% endif %}

  {# CANONICAL URL (deprecated) #}
  {% if theme_canonical_url and not pageurl %}
    <link rel="canonical" href="{{ theme_canonical_url }}{{ pagename }}.html"/>
  {% endif %}

  {# CANONICAL URL #}
  {%- if pageurl %}
    <link rel="canonical" href="{{ pageurl|e }}" />
  {%- endif %}

  {# JAVASCRIPTS #}
  {%- block scripts %}
  <!--[if lt IE 9]>
    <script src="{{ pathto('_static/js/html5shiv.min.js', 1) }}"></script>
  <![endif]-->
  {%- if not embedded %}
  {# XXX Sphinx 1.8.0 made this an external js-file, quick fix until we refactor the template to inherert more blocks directly from sphinx #}
    {% if sphinx_version >= "1.8.0" %}
      <script type="text/javascript" id="documentation_options" data-url_root="{{ url_root }}" src="{{ pathto('_static/documentation_options.js', 1) }}"></script>
      {%- for scriptfile in script_files %}
        {{ js_tag(scriptfile) }}
      {%- endfor %}
    {% else %}
      <script type="text/javascript">
          var DOCUMENTATION_OPTIONS = {
              URL_ROOT:'{{ url_root }}',
              VERSION:'{{ release|e }}',
              LANGUAGE:'{{ language }}',
              COLLAPSE_INDEX:false,
              FILE_SUFFIX:'{{ '' if no_search_suffix else file_suffix }}',
              HAS_SOURCE:  {{ has_source|lower }},
              SOURCELINK_SUFFIX: '{{ sourcelink_suffix }}'
          };
      </script>
      {%- for scriptfile in script_files %}
        <script type="text/javascript" src="{{ pathto(scriptfile, 1) }}"></script>
      {%- endfor %}
    {% endif %}
    <script type="text/javascript" src="{{ pathto('_static/js/theme.js', 1) }}"></script>

    {# OPENSEARCH #}
    {%- if use_opensearch %}
    <link rel="search" type="application/opensearchdescription+xml"
          title="{% trans docstitle=docstitle|e %}Search within {{ docstitle }}{% endtrans %}"
          href="{{ pathto('_static/opensearch.xml', 1) }}"/>
    {%- endif %}
  {%- endif %}
  {%- endblock %}

  {%- block linktags %}
    {%- if hasdoc('about') %}
    <link rel="author" title="{{ _('About these documents') }}" href="{{ pathto('about') }}" />
    {%- endif %}
    {%- if hasdoc('genindex') %}
    <link rel="index" title="{{ _('Index') }}" href="{{ pathto('genindex') }}" />
    {%- endif %}
    {%- if hasdoc('search') %}
    <link rel="search" title="{{ _('Search') }}" href="{{ pathto('search') }}" />
    {%- endif %}
    {%- if hasdoc('copyright') %}
    <link rel="copyright" title="{{ _('Copyright') }}" href="{{ pathto('copyright') }}" />
    {%- endif %}
    {%- if next %}
    <link rel="next" title="{{ next.title|striptags|e }}" href="{{ next.link|e }}" />
    {%- endif %}
    {%- if prev %}
    <link rel="prev" title="{{ prev.title|striptags|e }}" href="{{ prev.link|e }}" />
    {%- endif %}
  {%- endblock %}
  {%- block extrahead %} {% endblock %}
</head>

<body class="wy-body-for-nav">

  {% block extrabody %} {% endblock %}
  <div class="wy-grid-for-nav">
    {# SIDE NAV, TOGGLES ON MOBILE #}
    {%- if not embedded %}
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" {% if theme_style_nav_header_background %} style="background: {{theme_style_nav_header_background}}" {% endif %}>
          {% block sidebartitle %}

          {% if logo and theme_logo_only %}
            <a href="{{ pathto(master_doc) }}">
          {% else %}
            <a href="{{ pathto(master_doc) }}" class="icon icon-home"> {{ project }}
          {% endif %}

          {% if logo %}
            {# Not strictly valid HTML, but it's the only way to display/scale
               it properly, without weird scripting or heaps of work
            #}
            <img src="{{ pathto('_static/' + logo, 1) }}" class="logo" alt="{{ _('Logo') }}"/>
          {% endif %}
          </a>

          {% if theme_display_version %}
            {%- set nav_version = version %}
            {% if READTHEDOCS and current_version %}
              {%- set nav_version = current_version %}
            {% endif %}
            {% if nav_version %}
              <div class="version">
                {{ nav_version }}
              </div>
            {% endif %}
          {% endif %}

          {% include "searchbox.html" %}

          {% endblock %}
        </div>

        {% block navigation %}
        <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
          {% block menu %}
            {#
              The singlehtml builder doesn't handle this toctree call when the
              toctree is empty. Skip building this for now.
            #}
            {% if 'singlehtml' not in builder %}
              {% set global_toc = toctree(maxdepth=theme_navigation_depth|int,
                                          collapse=theme_collapse_navigation|tobool,
                                          includehidden=theme_includehidden|tobool,
                                          titles_only=theme_titles_only|tobool) %}
            {% endif %}
            {% if global_toc %}
              {{ global_toc }}
            {% else %}
              <!-- Local TOC -->
              <div class="local-toc">{{ toc }}</div>
            {% endif %}
          {% endblock %}
        </div>
        {% endblock %}
      </div>
    </nav>
    {%- endif %}

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">

      {# MOBILE NAV, TRIGGLES SIDE NAV ON TOGGLE #}
      {% if not embedded %}
      <nav class="wy-nav-top" aria-label="top navigation">
        {% block mobile_nav %}
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="{{ pathto(master_doc) }}">{{ project }}</a>
        {% endblock %}
      </nav>
      {% endif %}


      <div class="wy-nav-content">
      {%- block content %}
        {% if theme_style_external_links|tobool %}
        <div class="rst-content style-external-links">
        {% else %}
        <div class="rst-content">
        {% endif %}
          {# Don't show breadcrumbs on embedded #}
          {% if not embedded %}
          {% include "breadcrumbs.html" %}
          {% endif %}
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
          {%- block document %}
           <div itemprop="articleBody">
            {% block body %}{% endblock %}
           </div>
           {% if self.comments()|trim %}
           <div class="articleComments">
            {% block comments %}{% endblock %}
           </div>
           {% endif%}
          </div>
          {%- endblock %}
          {% include "footer.html" %}
        </div>
      {%- endblock %}
      </div>

    </section>

  </div>
  {% include "versions.html" %}

  {# Do not insert script tag for htmlhelp #}
  {% if 'htmlhelp' not in builder %}
  <script type="text/javascript">
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable({{ 'true' if theme_sticky_navigation|tobool else 'false' }});
      });
  </script>
  {% endif %}

  {# Do not conflict with RTD insertion of analytics script #}
  {% if not READTHEDOCS %}
    {% if theme_analytics_id %}
    <!-- Theme Analytics -->
    <script>
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

    ga('create', '{{ theme_analytics_id }}', 'auto');
    {% if theme_analytics_anonymize_ip|tobool %}
    ga('set', 'anonymizeIp', true);
    {% endif %}
    ga('send', 'pageview');
    </script>

    {% endif %}
  {% endif %}

  {%- block footer %} {% endblock %}

</body>
</html>
