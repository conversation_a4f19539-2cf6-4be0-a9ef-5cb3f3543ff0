API Reference: qrenderdoc UI Extensions
=======================================

This is the API reference for the functions, classes, and enums in the ``qrenderdoc`` module which represents the UI-specific interface for integrating with the UI and writing UI extensions. For more high-level information and instructions on using the python API, see :doc:`../index` and :doc:`../ui_extensions`.

.. contents:: Sections
   :local:

.. currentmodule:: qrenderdoc

Extension Manager
-----------------

.. autoclass:: qrenderdoc.ExtensionManager
  :members:

Mini-Qt Helper
--------------

.. autoclass:: qrenderdoc.MiniQtHelper
  :members:

Helpers
-------

.. autoclass:: qrenderdoc.ExtensionMetadata
  :members:

.. autoclass:: qrenderdoc.WindowMenu
  :members:
  :exclude-members: enum_constants__, 

.. autoclass:: qrenderdoc.PanelMenu
  :members:
  :exclude-members: enum_constants__, 

.. autoclass:: qrenderdoc.ContextMenu
  :members:
  :exclude-members: enum_constants__, 

.. autoclass:: qrenderdoc.DialogButton
  :members:
  :exclude-members: enum_constants__, 

