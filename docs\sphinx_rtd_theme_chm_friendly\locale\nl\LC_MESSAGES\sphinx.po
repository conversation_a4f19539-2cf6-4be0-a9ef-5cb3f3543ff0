# English translations for sphinx_rtd_theme.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# <PERSON>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 0.4.3.dev0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2021-01-04 13:48-0800\n"
"PO-Revision-Date: 2019-07-16 21:44+0000\n"
"Last-Translator: <PERSON>, 2019\n"
"Language-Team: Dutch (https://www.transifex.com/readthedocs/teams/101354/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: sphinx_rtd_theme/breadcrumbs.html:45 sphinx_rtd_theme/breadcrumbs.html:47
msgid "Edit on GitHub"
msgstr "Bewerk op GitHub"

#: sphinx_rtd_theme/breadcrumbs.html:52 sphinx_rtd_theme/breadcrumbs.html:54
msgid "Edit on Bitbucket"
msgstr "Bewerk op BitBucket"

#: sphinx_rtd_theme/breadcrumbs.html:59 sphinx_rtd_theme/breadcrumbs.html:61
msgid "Edit on GitLab"
msgstr "Bewerk op GitLab"

#: sphinx_rtd_theme/breadcrumbs.html:64 sphinx_rtd_theme/breadcrumbs.html:66
msgid "View page source"
msgstr "Bekijk paginabron"

#: sphinx_rtd_theme/breadcrumbs.html:76 sphinx_rtd_theme/footer.html:5
msgid "Next"
msgstr "Volgende"

#: sphinx_rtd_theme/breadcrumbs.html:79 sphinx_rtd_theme/footer.html:8
msgid "Previous"
msgstr "Vorige"

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:29
msgid "Build"
msgstr "Bouwresultaat"

#. the phrase "revision" comes from Git, referring to a commit
#: sphinx_rtd_theme/footer.html:35
msgid "Revision"
msgstr "Revisie"

#: sphinx_rtd_theme/footer.html:40
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Laatste update op %(last_updated)s."

#. the variable "sphinx_web" is a link to the Sphinx project documentation
#. with
#. the text "Sphinx"
#: sphinx_rtd_theme/footer.html:52
#, python-format
msgid "Built with %(sphinx_web)s using a"
msgstr "Gebouwd met %(sphinx_web)s met een"

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documenation
#: sphinx_rtd_theme/footer.html:54
msgid "theme"
msgstr "thema"

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:56
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr "geleverd door %(readthedocs_web)s"

#: sphinx_rtd_theme/layout.html:85
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Zoek binnen %(docstitle)s"

#: sphinx_rtd_theme/layout.html:93
msgid "About these documents"
msgstr "Over deze documenten"

#: sphinx_rtd_theme/layout.html:96
msgid "Index"
msgstr "Index"

#: sphinx_rtd_theme/layout.html:99 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr "Zoek"

#: sphinx_rtd_theme/layout.html:102
msgid "Copyright"
msgstr "Copyright"

#: sphinx_rtd_theme/layout.html:134
msgid "Logo"
msgstr "Logo"

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr "Zet JavaScript aan om de zoekfunctie mogelijk te maken."

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr "Zoekresultaten"

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""
"Zoekpoging vond geen documenten. Zorg ervoor dat alle woorden correct zijn "
"gespeld en dat voldoende categorieën zijn geselecteerd."

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr "Zoek in documentatie"

#: sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr "Versies"

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr "Downloads"

#. The phrase "Read the Docs" is not translated
#: sphinx_rtd_theme/versions.html:24
msgid "On Read the Docs"
msgstr "Op Read the Docs"

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr "Project Home"

#: sphinx_rtd_theme/versions.html:29
msgid "Builds"
msgstr "Bouwresultaten"

#~ msgid "Docs"
#~ msgstr "Documentatie"

#~ msgid "Free document hosting provided by"
#~ msgstr "Gratis hosting voor documentatie verzorgd door"
