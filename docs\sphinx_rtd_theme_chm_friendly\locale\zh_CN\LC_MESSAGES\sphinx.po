# English translations for sphinx_rtd_theme.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 0.4.3.dev0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2021-01-04 13:48-0800\n"
"PO-Revision-Date: 2019-07-16 21:44+0000\n"
"Last-Translator: 王赛 <<EMAIL>>, 2020\n"
"Language-Team: Chinese (China) (https://www.transifex.com/readthedocs/teams/101354/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: sphinx_rtd_theme/breadcrumbs.html:45 sphinx_rtd_theme/breadcrumbs.html:47
msgid "Edit on GitHub"
msgstr "在 GitHub 上修改"

#: sphinx_rtd_theme/breadcrumbs.html:52 sphinx_rtd_theme/breadcrumbs.html:54
msgid "Edit on Bitbucket"
msgstr "在 Bitbucket 上修改"

#: sphinx_rtd_theme/breadcrumbs.html:59 sphinx_rtd_theme/breadcrumbs.html:61
msgid "Edit on GitLab"
msgstr "在 GitLab 上修改"

#: sphinx_rtd_theme/breadcrumbs.html:64 sphinx_rtd_theme/breadcrumbs.html:66
msgid "View page source"
msgstr "查看页面源码"

#: sphinx_rtd_theme/breadcrumbs.html:76 sphinx_rtd_theme/footer.html:5
msgid "Next"
msgstr "下一页"

#: sphinx_rtd_theme/breadcrumbs.html:79 sphinx_rtd_theme/footer.html:8
msgid "Previous"
msgstr "上一页"

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:29
msgid "Build"
msgstr "构建"

#: sphinx_rtd_theme/footer.html:40
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "最后更新时间 %(last_updated)s。"

#. the variable "sphinx_web" is a link to the Sphinx project documentation
#. with
#. the text "Sphinx"
#: sphinx_rtd_theme/footer.html:52
#, python-format
msgid "Built with %(sphinx_web)s using a"
msgstr "利用 %(sphinx_web)s 构建，使用了 "

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documenation
#: sphinx_rtd_theme/footer.html:54
msgid "theme"
msgstr "主题"

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:56
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr "由 %(readthedocs_web)s开发"

#: sphinx_rtd_theme/layout.html:85
#, python-format
msgid "Search within %(docstitle)s"
msgstr "在 %(docstitle)s中搜索"

#: sphinx_rtd_theme/layout.html:93
msgid "About these documents"
msgstr "关于此文档"

#: sphinx_rtd_theme/layout.html:96
msgid "Index"
msgstr "索引"

#: sphinx_rtd_theme/layout.html:99 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr "搜索"

#: sphinx_rtd_theme/layout.html:102
msgid "Copyright"
msgstr "版权所有"

#: sphinx_rtd_theme/layout.html:134
msgid "Logo"
msgstr "Logo"

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr "请启用 JavaScript 以便使用搜索功能"

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr "搜索结果"

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "您的搜索没有匹配到任何文档。请确保所有单词拼写正确，并选择了足够多的类别。"

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr "在文档中搜索"

#: sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr "版本列表"

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr "下载链接"

#. The phrase "Read the Docs" is not translated
#: sphinx_rtd_theme/versions.html:24
msgid "On Read the Docs"
msgstr "托管于 Read the Docs"

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr "项目首页"

#: sphinx_rtd_theme/versions.html:29
msgid "Builds"
msgstr "构建"

#~ msgid "Docs"
#~ msgstr "文档"

#~ msgid "Free document hosting provided by"
#~ msgstr "此文档免费托管于"
