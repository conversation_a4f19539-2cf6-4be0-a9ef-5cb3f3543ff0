# This file lists minimum generator versions required for known packages / files.
# hash marks start line comments.
# name is either a package name (as used in import) or a predefined name in parentheses.
# version is two decimal numbers divided by a dot.
# settings equally apply to all platforms (jython, cpython, ipy).

(default) 1.127 # anything not explicitly marked

(built-in) 1.145 # skeletons of all built-in modules are built together
# Note: modules like itertools, etc are "(built-in)" and are ignored if given separately

_fileio 1.127
_io 1.127
sys 1.127
thread 1.127
_thread 1.127
_struct 1.127
datetime 1.127
_collections 1.127

PyQt4.Qsci 1.127
PyQt4.QtAssistant 1.127
PyQt4.QtCore 1.127
PyQt4.QtDesigner 1.127
PyQt4.QtGui 1.127
PyQt4.QtHelp 1.127
PyQt4.QtNetwork 1.127
PyQt4.QtScriptTools 1.127
PyQt4.QtScript 1.127
PyQt4.QtSvg 1.127
PyQt4.QtTest 1.127
PyQt4.Qt 1.127
PyQt4.QtWebKit 1.127
PyQt4.QtXmlPatterns 1.127
PyQt4.QtXml 1.127

pygame.fastevent 1.127
pygame.image 1.127

sip 1.127

pysqlite2._sqlite 1.127
_bsddb 1.127

h5py.h5  1.127
h5py.h5i 1.127
h5py.h5g 1.127

numpy.random.mtrand 1.140
numpy.core.multiarray 1.143