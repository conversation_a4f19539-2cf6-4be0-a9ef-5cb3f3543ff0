Introduction
============

.. include:: include/intro_header_inc.rst

License
-------

RenderDoc is released under the MIT license, so there are no restrictions on your use of it either commercially or non-commercially. This includes the source release available on `GitHub <https://github.com/baldurk/renderdoc>`_.

Details of the licenses of third party software used in RenderDoc are included in the LICENSE file in the RenderDoc directory as well as in the :doc:`credits_acknowledgements`.

How to browse this documentation
--------------------------------

If you just want to dive straight in and learn how to get started using RenderDoc consider looking at the :doc:`getting_started/quick_start` section which gives a simple introduction on how to get started. Afterwards there are reference pages on each of the windows available in RenderDoc which can be referred to as you use the program, or skimmed to get a feel for the functionality available.


Those of you familiar with other graphics debuggers will likely find much of RenderDoc recognisable, you might want to check out the :doc:`getting_started/faq`, :doc:`getting_started/features`, :doc:`getting_started/tips_tricks` or browse the :doc:`how/index` sections which detail how some common tasks are accomplished.

Regardless of your experience or patience for documentation it's recommended that you read the :doc:`getting_started/gotchas_known_issues` as this details current limitations of the program.

Important notes
---------------

* RenderDoc is generally stable but you will still likely encounter bugs depending on your use case, especially as with graphics there is a lot of potential surface area and interactions. I am *always* happy to spend time to fix bugs, but bear in mind that I need information about how to reproduce your specific situation. If you're able to provide public reproducible steps please file an issue on GitHub, or if you need to share information privately please `email me <mailto:<EMAIL>?subject=RenderDoc%20bug>`_.
* On the other side of the coin, please do give feedback when RenderDoc works for you and request features that would make your life easier or improve your workflow.
* There are a few common issues you might run into, so if you have any problems check the :doc:`getting_started/faq`, or the `GitHub issues list <https://github.com/baldurk/renderdoc/issues>`_ to see if it's been reported.

Documentation and Tutorials
---------------------------

Aside from the documentation that you are reading, I have recorded some `YouTube video tutorials <http://www.youtube.com/user/baldurkarlsson/>`_ showing the use of some basic features and an introduction/overview. Note that these are quite old, from the first release of RenderDoc, but the basic workflows and operation is still similar.

There is also a great presentation by `@Icetigris <https://twitter.com/Icetigris>`_ which goes into some details of how RenderDoc can be used in real world situations: `Slides are available here <https://docs.google.com/presentation/d/1LQUMIld4SGoQVthnhT1scoA3k4Sg0as14G4NeSiSgFU/>`_

Contact info, Feedback, Bug reports
-----------------------------------

If you want to get in touch with any feature requests, suggestions, comments etc then feel free to contact me: `Contact me <mailto:<EMAIL>?subject=RenderDoc%20feedback>`_.

Bug reports can be submitted directly `via email <mailto:<EMAIL>?subject=RenderDoc%20bug>`_, or also on the `GitHub issues list <https://github.com/baldurk/renderdoc/issues>`_ where you can also find the full source code.

The `Builds page <https://renderdoc.org/builds>`_ always has the latest stable build downloads, as well as builds made from the source code each night for the bleeding edge. Stable releases update every two months or so and are the typical releases to stick to for the most reliable experience.

See Also
--------

* `RenderDoc Homepage <https://renderdoc.org/>`_
* `Builds page <https://renderdoc.org/builds>`_
