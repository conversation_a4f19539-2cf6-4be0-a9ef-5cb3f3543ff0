API Reference: qrenderdoc Main Interfaces
=========================================

This is the API reference for the functions, classes, and enums in the ``qrenderdoc`` module which represents the UI-specific interface for integrating with the UI and writing UI extensions. For more high-level information and instructions on using the python API, see :doc:`../index` and :doc:`../ui_extensions`.

.. contents:: Sections
   :local:

.. module:: qrenderdoc

Context
-------

.. autoclass:: qrenderdoc.CaptureContext
  :members:

Replay Manager
--------------

.. autoclass:: qrenderdoc.ReplayManager
  :members:

RGP Interop Control
-------------------

.. autoclass:: qrenderdoc.RGPInterop
  :members:


CaptureViewer Interface
------------------------

.. autoclass:: qrenderdoc.CaptureViewer
  :members:

Utilities
---------

.. autoclass:: qrenderdoc.DockReference
  :members:
  :exclude-members: enum_constants__, 

.. autoclass:: qrenderdoc.CaptureModifications
  :members:
  :exclude-members: enum_constants__, 

.. autoclass:: qrenderdoc.CaptureSettings
  :members:

.. autoclass:: qrenderdoc.EventBookmark
  :members:

