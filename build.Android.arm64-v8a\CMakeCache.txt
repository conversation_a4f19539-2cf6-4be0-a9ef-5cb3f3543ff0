# This is the CMakeCache file.
# For build in directory: f:/UGit/renderdoc/build.Android.arm64-v8a
# It was generated by CMake: D:/SDKTools/Android/SDK/cmake/3.16.8/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//The Android ABI to build for
ANDROID_ABI:STRING=arm64-v8a

//Version of Android build-tools to use instead of the default
ANDROID_BUILD_TOOLS_VERSION:STRING=

//No help, variable specified on the command line.
ANDROID_NDK_ROOT_PATH:UNINITIALIZED=D:\SDKTools\Android\android-ndk-r21e

//No help, variable specified on the command line.
ANDROID_SDK_ROOT_PATH:UNINITIALIZED=D:\SDKTools\Android\SDK

//The Target ID to build the APK for like 'android-99', use <android
// list targets> to choose another one.
APK_TARGET_ID:STRING=android-28

//No help, variable specified on the command line.
BUILD_ANDROID:UNINITIALIZED=On

//The URL or email to contact with issues. See DISTRIBUTION_CONTACT
// in renderdoc/api/replay/version.h
BUILD_VERSION_DIST_CONTACT:STRING=

//The name of the distribution. See DISTRIBUTION_NAME in renderdoc/api/replay/version.h
BUILD_VERSION_DIST_NAME:STRING=

//The distribution-specific version number. See DISTRIBUTION_VERSION
// in renderdoc/api/replay/version.h
BUILD_VERSION_DIST_VER:STRING=

//The current git commit hash. See renderdoc/replay/version.cpp
BUILD_VERSION_HASH:STRING=

//If this is a stable build. See RENDERDOC_STABLE_BUILD in renderdoc/api/replay/version.h
BUILD_VERSION_STABLE:BOOL=OFF

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-addr2line.exe

//No help, variable specified on the command line.
CMAKE_ANDROID_ARCH_ABI:UNINITIALIZED=arm64-v8a

//No help, variable specified on the command line.
CMAKE_ANDROID_STL_TYPE:UNINITIALIZED=c++_static

//Archiver
CMAKE_AR:FILEPATH=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-ar.exe

//Flags used by the compiler during all build types.
CMAKE_ASM_FLAGS:STRING=

//Flags used by the compiler during debug builds.
CMAKE_ASM_FLAGS_DEBUG:STRING=

//Flags used by the compiler during release builds.
CMAKE_ASM_FLAGS_RELEASE:STRING=

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Release

//LLVM archiver
CMAKE_CXX_COMPILER_AR:FILEPATH=CMAKE_CXX_COMPILER_AR-NOTFOUND

//Generate index for LLVM archive
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=CMAKE_CXX_COMPILER_RANLIB-NOTFOUND

//Flags used by the compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the compiler during debug builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the compiler during release builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=-latomic -lm

//LLVM archiver
CMAKE_C_COMPILER_AR:FILEPATH=CMAKE_C_COMPILER_AR-NOTFOUND

//Generate index for LLVM archive
CMAKE_C_COMPILER_RANLIB:FILEPATH=CMAKE_C_COMPILER_RANLIB-NOTFOUND

//Flags used by the compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the compiler during debug builds.
CMAKE_C_FLAGS_DEBUG:STRING=

//No help, variable specified on the command line.
CMAKE_C_FLAGS_DEBUG_INIT:UNINITIALIZED=-g3 -ggdb3

//No help, variable specified on the command line.
CMAKE_C_FLAGS_INIT:UNINITIALIZED=-ansi -Wall

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the compiler during release builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//No help, variable specified on the command line.
CMAKE_C_FLAGS_RELEASE_INIT:UNINITIALIZED=-O3 -DNDEBUG -s

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=-latomic -lm

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//No help, variable specified on the command line.
CMAKE_EXE_LINKER_FLAGS_INIT:UNINITIALIZED=-Wl,--strip-debug

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=OFF

//No help, variable specified on the command line.
CMAKE_GENERATOR:UNINITIALIZED=Ninja

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/RenderDoc

//Path to a program.
CMAKE_LINKER:FILEPATH=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-ld.exe

//No help, variable specified on the command line.
CMAKE_MAKE_PROGRAM:UNINITIALIZED=D:\SDKTools\Android\SDK\cmake\3.22.1\bin\ninja.exe

//Flags used by the linker during the creation of modules.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-nm.exe

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-objcopy.exe

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-objdump.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=RenderDoc

//Ranlib
CMAKE_RANLIB:FILEPATH=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-ranlib.exe

//Path to a program.
CMAKE_READELF:FILEPATH=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-readelf.exe

//Flags used by the linker during the creation of dll's.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=D:/SDKTools/Android/SDK/ndk/20.1.5948944/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android-strip.exe

//No help, variable specified on the command line.
CMAKE_SYSTEM_NAME:UNINITIALIZED=Android

//No help, variable specified on the command line.
CMAKE_SYSTEM_VERSION:UNINITIALIZED=21

//The CMake toolchain file
CMAKE_TOOLCHAIN_FILE:FILEPATH=D:/SDKTools/Android/SDK/ndk/20.1.5948944/build/cmake/android.toolchain.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Enable address sanitizer
ENABLE_ASAN:BOOL=OFF

//Enable custom wrap.sh on Android to workaround Android bug
ENABLE_CUSTOM_WRAP_SCRIPT:BOOL=ON

//Enable dlsym() hooking via glibc internals
ENABLE_DLSYM_HOOKING:BOOL=OFF

ENABLE_EGL:BOOL=ON

//Enable GGP support
ENABLE_GGP:BOOL=OFF

ENABLE_GL:BOOL=OFF

ENABLE_GLES:BOOL=ON

//Enable Metal driver
ENABLE_METAL:BOOL=OFF

//Enable memory sanitizer
ENABLE_MSAN:BOOL=OFF

ENABLE_PYRENDERDOC:BOOL=OFF

ENABLE_QRENDERDOC:BOOL=OFF

//Enable renderdoccmd
ENABLE_RENDERDOCCMD:BOOL=ON

//Enable thread sanitizer
ENABLE_TSAN:BOOL=OFF

//Enable EXPERIMENTAL, POSSIBLY BROKEN, UNSUPPORTED wayland windowing
// support
ENABLE_UNSUPPORTED_EXPERIMENTAL_POSSIBLY_BROKEN_WAYLAND:BOOL=OFF

//Enable Vulkan driver
ENABLE_VULKAN:BOOL=ON

//Enable xcb windowing support
ENABLE_XCB:BOOL=ON

//Enable xlib windowing support
ENABLE_XLIB:BOOL=ON

//Command to run to compile a .cpp into an executable. Default
// is just c++
HOST_NATIVE_CPP_COMPILER:STRING=c++

//Internal option: enable self-capture
INTERNAL_SELF_CAPTURE:BOOL=OFF

//Path to a program.
Java_IDLJ_EXECUTABLE:FILEPATH=D:/SDKTools/Java/jdk1.8.0_291/bin/idlj.exe

//Path to a program.
Java_JARSIGNER_EXECUTABLE:FILEPATH=D:/SDKTools/Java/jdk1.8.0_291/bin/jarsigner.exe

//Path to a program.
Java_JAR_EXECUTABLE:FILEPATH=D:/SDKTools/Java/jdk1.8.0_291/bin/jar.exe

//Path to a program.
Java_JAVAC_EXECUTABLE:FILEPATH=D:/SDKTools/Java/jdk1.8.0_291/bin/javac.exe

//Path to a program.
Java_JAVADOC_EXECUTABLE:FILEPATH=D:/SDKTools/Java/jdk1.8.0_291/bin/javadoc.exe

//Path to a program.
Java_JAVAH_EXECUTABLE:FILEPATH=D:/SDKTools/Java/jdk1.8.0_291/bin/javah.exe

//Path to a program.
Java_JAVA_EXECUTABLE:FILEPATH=D:/SDKTools/Java/jdk1.8.0_291/bin/java.exe

//Subfolder under the 'lib' folder in target directory structure.
// E.g. set to 'renderdoc' to use /usr/local/lib/renderdoc instead
// of /usr/local/lib.
LIB_SUBFOLDER:STRING=

//Suffix for 'lib' folder in target directory structure. E.g. set
// to '64' to use /usr/local/lib64 instead of /usr/local/lib.
LIB_SUFFIX:STRING=

//No help, variable specified on the command line.
LLVM_DIR:UNINITIALIZED=F:\UGit\llvm_4.0_src\llvm\build_native\install_arm64\lib\cmake\llvm

//Path to RenderDoc .apk files after installation of RenderDoc
// on host (either absolute or relative to binary)
RENDERDOC_APK_PATH:STRING=

//Path to RenderDoc plugins folder after installation of RenderDoc
// (either absolute or relative to binary)
RENDERDOC_PLUGINS_PATH:STRING=

//Value Computed by CMake
RenderDoc_BINARY_DIR:STATIC=F:/UGit/renderdoc/build.Android.arm64-v8a

//Value Computed by CMake
RenderDoc_SOURCE_DIR:STATIC=F:/UGit/renderdoc

//Strip the resulting android library
STRIP_ANDROID_LIBRARY:BOOL=On

//OFF
USE_INTERCEPTOR_LIB:BOOL=On

//Suffix for the vulkan implicit_layer json file. E.g. set to '.x86_64'
// to use renderdoc_capture.x86_64.json instead of renderdoc_capture.json
VULKAN_JSON_SUFFIX:STRING=

//Path to install the vulkan layer file
VULKAN_LAYER_FOLDER:PATH=/etc/vulkan/implicit_layer.d

//Dependencies for the target
renderdoc_LIB_DEPENDS:STATIC=general;-lm;general;-ldl;general;-llog;general;-landroid;general;-lz;general;-lLLVMAArch64AsmParser;general;-lLLVMAArch64Desc;general;-lLLVMAArch64Disassembler;general;-lLLVMAArch64AsmPrinter;general;-lLLVMAArch64Info;general;-lLLVMAArch64Utils;general;-lLLVMMCDisassembler;general;-lLLVMObject;general;-lLLVMMCParser;general;-lLLVMBitReader;general;-lLLVMMC;general;-lLLVMCore;general;-lLLVMSupport;general;-lrenderdoc_libentry;

//Dependencies for the target
renderdoccmd_LIB_DEPENDS:STATIC=general;renderdoc;general;-llog;general;-landroid;


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=f:/UGit/renderdoc/build.Android.arm64-v8a
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=16
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=8
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=D:/SDKTools/Android/SDK/cmake/3.16.8/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=D:/SDKTools/Android/SDK/cmake/3.16.8/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=D:/SDKTools/Android/SDK/cmake/3.16.8/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=F:/UGit/renderdoc
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=0
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=11
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=D:/SDKTools/Android/SDK/cmake/3.16.8/share/cmake-3.16
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Java
FIND_PACKAGE_MESSAGE_DETAILS_Java:INTERNAL=[D:/SDKTools/Java/jdk1.8.0_291/bin/java.exe][D:/SDKTools/Java/jdk1.8.0_291/bin/jar.exe][D:/SDKTools/Java/jdk1.8.0_291/bin/javac.exe][D:/SDKTools/Java/jdk1.8.0_291/bin/javah.exe][D:/SDKTools/Java/jdk1.8.0_291/bin/javadoc.exe][v1.8.0_291()]
//ADVANCED property for variable: Java_IDLJ_EXECUTABLE
Java_IDLJ_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Java_JARSIGNER_EXECUTABLE
Java_JARSIGNER_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Java_JAR_EXECUTABLE
Java_JAR_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Java_JAVAC_EXECUTABLE
Java_JAVAC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Java_JAVADOC_EXECUTABLE
Java_JAVADOC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Java_JAVAH_EXECUTABLE
Java_JAVAH_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Java_JAVA_EXECUTABLE
Java_JAVA_EXECUTABLE-ADVANCED:INTERNAL=1

